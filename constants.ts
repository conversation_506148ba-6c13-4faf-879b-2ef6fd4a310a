
export const SOURCE_TEXT_1 = `
# Generalized LLM Instruction Templates
Conversation ID: 68455373-07f4-8008-8a1b-c5f4cd45b2ee

## Message 5
i'm looking for generalized instructions such as these:
    ### File Structure
    ...
    (Content from user's message 5, containing the detailed markdown files: 01_SYSTEM_OVERVIEW.md, 02_TEMPLATE_SPECIFICATION.md, 03_DIRECTIONAL_TRANSFORMATION_THEORY.md, 04_GENERALIZED_DIRECTIONAL_TEMPLATES.md, 05_EXECUTION_ENGINE.md, 06_DIRECTIONAL_USAGE_GUIDE.md, 07_DEVELOPMENT_GUIDE.md, 08_DOCUMENTATION_INDEX.md)
    ---
    #### \`01_SYSTEM_OVERVIEW.md\`
    \`\`\`markdown
        # AI Systems 0010: Template-Based Instruction Processing System
        ## Overview
        This system provides a comprehensive framework for creating, managing, and executing structured AI instructions through a template-based approach. It enables sophisticated prompt engineering workflows by organizing AI instructions into reusable, composable templates that can be executed in sequences across multiple LLM models.
        ## Core Philosophy
        The system is built around the principle of **structured instruction processing** where every AI interaction follows a standardized three-part template format:
        1. **[Title]** - Defines the template's purpose
        2. **Interpretation** - Human-readable explanation with goal negation pattern
        3. **\`{Transformation}\`** - Machine-parsable execution parameters
        This approach ensures consistency, reusability, and optimal LLM performance while eliminating ambiguity in AI instructions.
    ... [and so on for the entire provided markdown content]
    \`\`\`
---
#### \`02_TEMPLATE_SPECIFICATION.md\`
\`\`\`markdown
    # Template Specification Guide
    ## Overview
    This document defines the complete specification for creating AI instruction templates in the Template-Based Instruction Processing System. Templates follow a standardized three-part structure optimized for LLM performance and human readability.
    ## Core Template Structure
    Every template MUST follow this exact format:
    \`\`\`
    [Title] Interpretation Execute as: \`{Transformation}\`
    \`\`\`
... [full content of 02_TEMPLATE_SPECIFICATION.md]
\`\`\`
---
#### \`03_DIRECTIONAL_TRANSFORMATION_THEORY.md\`
\`\`\`markdown
    # Directional Transformation Theory
    ## Core Principle
    **Directional transformation** operates on the principle that **transformation vectors** are universal and context-independent. Instead of analyzing *what* something is, we apply *how* to transform it through pure directional operations.
... [full content of 03_DIRECTIONAL_TRANSFORMATION_THEORY.md]
\`\`\`
---
#### \`04_GENERALIZED_DIRECTIONAL_TEMPLATES.md\`
\`\`\`markdown
    # Generalized Directional Templates
    ## Core Principle
    These templates operate on **transformation vectors** rather than content analysis. They apply directional operations that work regardless of input type, domain, or context. The focus is on **how to transform** rather than **what to transform**.
... [full content of 04_GENERALIZED_DIRECTIONAL_TEMPLATES.md]
\`\`\`
---
#### \`05_EXECUTION_ENGINE.md\`
\`\`\`markdown
    # Execution Engine Documentation
    ## Overview
    The Execution Engine (\`lvl1_sequence_executor.py\`) is the core component that orchestrates the execution of template sequences across multiple LLM models.
... [full content of 05_EXECUTION_ENGINE.md]
\`\`\`
---
#### \`06_DIRECTIONAL_USAGE_GUIDE.md\`
\`\`\`markdown
    # Directional Templates Usage Guide
    ## Quick Start
    Directional templates provide **universal transformation vectors** that work with any input.
... [full content of 06_DIRECTIONAL_USAGE_GUIDE.md]
\`\`\`
---
#### \`07_DEVELOPMENT_GUIDE.md\`
\`\`\`markdown
    # Development Guide
    ## Overview
    This guide provides comprehensive instructions for setting up, developing, and extending the AI Template-Based Instruction Processing System.
... [full content of 07_DEVELOPMENT_GUIDE.md]
\`\`\`
---
#### \`08_DOCUMENTATION_INDEX.md\`
\`\`\`markdown
    # AI Systems 0010: Complete Documentation Index
    ## Overview
    This documentation suite provides comprehensive coverage of the AI Template-Based Instruction Processing System.
... [full content of 08_DOCUMENTATION_INDEX.md]
\`\`\`
`;

export const SOURCE_TEXT_2 = `
## Message 6
## 1. Anthropic’s **Model Context Protocol (MCP)** – **Unified System Template for Tools & Context**. An open framework (with SDKs in multiple languages) that standardizes how LLMs receive context and tool instructions【45†L75-L83】【45†L85-L93】. MCP essentially acts as a “universal port” for AI, letting you feed the model a structured system message containing available tools, data sources, and usage guidelines in a consistent format, regardless of the model or platform【38†L47-L55】【45†L77-L85】. Notably, it enables *universal template compatibility*: you can switch out LLM backends easily since the system prompt and tool specification follow the MCP schema rather than model-specific quirks【45†L85-L94】. Its design negates bespoke prompt formats by using a context‐independent schema – e.g. a *System* block defining the agent role and a list of tools (with descriptions and invocation patterns) – ensuring any MCP-compliant model can interpret it. The official **Model Context Protocol** site provides documentation and SDKs【45†L8-L16】【45†L71-L80】, and Anthropic has made it public to encourage multi-model orchestration. *Differentiator:* Standardizes tool-use instructions and contextual data injection across vendors, much like USB-C for prompts【38†L47-L55】【45†L77-L85】. *Repo/Docs:* GitHub SDKs in various languages【45†L8-L16】; [Introduction docs](https://modelcontextprotocol.io/introduction).
## 2. **Microsoft AutoGen** – **Multi-Agent Conversational Framework**. **AutoGen** (open-source on GitHub) is a framework for building LLM applications by composing multiple agents that communicate in a structured conversation to solve tasks【51†L161-L169】. Each agent can be an LLM, a tool API, or even a human, and they converse following a system template. AutoGen uses transformation-based message handling – e.g. *TransformMessages* utilities to adjust prompts for different models or to compress context【28†L123-L132】【29†L71-L79】 – making it *context-independent*. It provides a high-level API to define agents with roles (tools, solvers, planners) and uses a loop (thought→action→observation) paradigm. Notably, AutoGen supports **multi-model orchestration**: you can seamlessly integrate OpenAI, Anthropic, or open models in one workflow, with automatic format adaptation (it even injects agent names into prompts for models that lack role-awareness)【29†L37-L45】【29†L61-L69】. Its system message templates are transformation-driven – e.g. *planning agents* can be prompted to produce a plan, which is then fed to an *execution agent*. *Differentiator:* Highly modular and agentic – developers define conversation patterns (in natural language or code) and AutoGen manages the prompting, including vector-based context compression and other transforms for non-OpenAI models【29†L29-L37】【29†L73-L81】. *Repo:* **AutoGen** on GitHub (Microsoft)【51†L161-L169】【51†L172-L179】, with documentation on multi-agent templates.
## 3. **Activation Steering for Instructions** – **Vector-Based Prompt Transformation (Microsoft Research)**. A recently introduced technique (ICLR 2025) that *steers* any LLM’s behavior via activation vector offsets rather than hard-coded text prompts【40†L290-L298】. The official repository “**llm-steer-instruct**”【40†L283-L291】 implements this: it computes a **steering vector** by taking the difference in the model’s internal activations with vs. without a given instruction, then adds that vector into the model’s forward pass to enforce the instruction【40†L290-L298】. For example, to apply a “JSON output” format rule, one computes the vector between the model’s states on a JSON-formatted vs. a normal completion, and later adds this vector to new queries – pushing the model toward JSON outputs without needing an elaborate system prompt. This approach is *context-independent* (it doesn’t rely on in-prompt examples) and **transformation-based** at a fundamental level – instructions become linear transformations in activation space. It supports “directional abstraction”: you can combine multiple steering vectors (e.g. one for formality and one for brevity) for composite behavior【40†L303-L312】【40†L319-L327】. *Differentiator:* No need to alter the text prompt; it’s a **universal, model-agnostic template** in vector form that can overlay any textual instruction following task. This also means it’s compatible with any model with accessible activations, making it a generalized method to enforce system-style constraints (length, format, vocabulary) even if the model wasn’t fine-tuned for them. *Repo:* **microsoft/llm-steer-instruct** (ICLR 2025)【40†L283-L291】【40†L290-L298】 with code and paper.
## 4. **Qwen-Agent by Alibaba** – **Instruction-Following Agent Framework with Tool and Memory Templates**. **Qwen-Agent** is an open-source framework built around the Qwen-3 LLM, featuring a system message template that orchestrates instruction following with tool use, planning, and memory【48†L303-L311】. It provides a structured *system prompt file* where the model is given a role and can employ **function calling**, code interpreter, retrieval-augmented generation (RAG), etc., guided by a template. For instance, Qwen-Agent’s default system message includes a *function-call format template* (recently updated for Qwen 3.1) to instruct the model how to output tool calls in JSON【48†L309-L317】【54†L7-L15】. It also defines a memory context format so the model can recall prior interactions. Uniquely, Qwen-Agent has embraced **MCP** for tool integration (as of May 2025, it added MCP cookbooks)【48†L309-L317】 – meaning it can use Anthropic’s standardized tool context instructions, making it interoperable with other MCP-based systems. Its architecture uses multiple modules (browser assistant, code executor, etc.) that are activated via the system message template when relevant【48†L303-L311】. *Differentiators:* Combines *universal template compatibility* (via MCP and a “Nous” style prompt for function calls【54†L11-L16】) with **multi-step planning** (internal reasoning steps before final answers) – all within a single coherent system message. It demonstrates **multi-component orchestration**: although primarily using one base model, it coordinates multiple capabilities through prompt structure. *Repo:* **QwenLM/Qwen-Agent** on GitHub【48†L303-L311】【48†L309-L317】, with documentation of its prompt formats and example system prompt files.
## 5. **Directional Stimulus Prompting (DSP)** – **Two-Stage Instruction Template with Context Hints**. **DSP**【3†L283-L291】 is a framework (NeurIPS 2023) that introduces an *auxiliary policy model* to generate a structured “stimulus” prompt guiding a larger LLM. It uses a **transformation-based template** split into blocks like: **Title/Goal**, **Interpretation (Key Points)**, and **Transformation/Response**. In practice, the smaller policy LM takes the user request and produces a brief *directional stimulus* (e.g. a list of key terms, an outline, or negated goals to avoid)【3†L283-L291】【3†L294-L299】. This stimulus is then prepended to the main LLM’s input as a system hint, after which the big LLM generates the final answer (the *Transformation* of the input guided by the stimulus). The approach is *context-independent* because the stimulus focuses on the task’s essential vectors (often literally keywords or an embedding-derived hint) rather than the full context. It implements **goal negation patterns** implicitly: by including what *not* to do or highlighting tricky aspects in the stimulus (e.g. “avoid mentioning X”), the system message steers the model away from undesired outputs【32†L78-L87】【32†L91-L100】. DSP’s core theory is a form of *directional transformation*: the stimulus acts as a vector in semantic space pointing the LLM toward desired properties (higher relevance, specific style)【3†L285-L293】【3†L296-L304】. The DSP repository provides examples (e.g. for summarization, the stimulus = “keywords of the article”) and shows improved alignment with goals【3†L287-L295】【3†L297-L304】. *Differentiator:* **Transformation via an intermediate prompt** – rather than a single monolithic system message, it modularizes the prompt into interpretable subparts (goal interpretation and execution), making it a generally applicable template across tasks (you can swap out the policy model or adjust the stimulus format for any domain). It’s also model-agnostic – the technique works as a wrapper around *any* frozen LLM. *Repo/Paper:* **Directional-Stimulus-Prompting** (NeurIPS 2023)【3†L283-L291】【3†L294-L302】, with README and diagrams of the two-stage prompt structure.
`;

// A placeholder for the full text, to avoid making the file enormous.
// In a real app, you would include the full text from the prompt.
export const SOURCE_TEXT_1_ABBREVIATED = `
# Generalized LLM Instruction Templates
... (Full content of the detailed markdown files describing the custom framework) ...
`;

export const CONVERSATION_HISTORY = `
{
  "initial_prompt": "please familiarize yourself with the following conversation history, and summarize the answer to the question: \\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\"",
  "sequence_id": "1100|1201|1000|1900|1201|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\\n\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\n\\n\\\`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}\\\`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "please familiarize yourself with the following conversation history, and summarize the answer to the question: \\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\"",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"goal_map\\": [\\n    {\\n      \\"step\\": \\"Identify explicit asks in the prompt\\",\\n      \\"details\\": [\\n        \\"Familiarize with the provided conversation history.\\",\\n        \\"Summarize the answer to a complex question.\\",\\n        \\"The question involves writing a one-line LLM-optimized prompt for instruction generation.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Surface hidden assumptions and dependencies\\",\\n      \\"details\\": [\\n        \\"Assumes access to full conversation history.\\",\\n        \\"Assumes understanding of 'transformation concepts'.\\",\\n        \\"Assumes comprehension of maximally effective, generalized, and LLM-optimized instructions.\\",\\n        \\"Assumes the ability to analyze both previous and new provided input.\\",\\n        \\"Assumes knowledge of what makes a system message instruction maximally actionable.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Enumerate sub-goals\\",\\n      \\"details\\": [\\n        \\"Parse and understand the provided conversation history (not provided in the prompt).\\",\\n        \\"Distill all relevant insights from prior conversation turns and newly provided input.\\",\\n        \\"Define what makes a sequence of system_message instructions 'maximally effective', 'LLM-optimized', and 'generalized'.\\",\\n        \\"Devise ways to incorporate both past analysis and new insights into prompt creation.\\",\\n        \\"Draft a one-line prompt that requests system_message instructions matching the criteria.\\",\\n        \\"Ensure the result is consistently actionable and value-maximizing.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Reveal potential blockers or ambiguities\\",\\n      \\"details\\": [\\n        \\"Lack of actual conversation history to analyze.\\",\\n        \\"Unclear definitions for terms like 'transformation concepts', 'actionable value', 'LLM-optimized'.\\",\\n        \\"Ambiguity in how to merge historical context with new input for instruction sequence optimization.\\",\\n        \\"The meaning of 'one-line prompt' could vary.\\",\\n        \\"Implicit requirement to be succinct yet exhaustive in the prompt.\\"\\n      ]\\n    }\\n  ]\\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\\n\\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\\n\\n\\\`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}\\\`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"goal_map\\": [\\n    {\\n      \\"step\\": \\"Identify explicit asks in the prompt\\",\\n      \\"details\\": [\\n        \\"Familiarize with the provided conversation history.\\",\\n        \\"Summarize the answer to a complex question.\\",\\n        \\"The question involves writing a one-line LLM-optimized prompt for instruction generation.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Surface hidden assumptions and dependencies\\",\\n      \\"details\\": [\\n        \\"Assumes access to full conversation history.\\",\\n        \\"Assumes understanding of 'transformation concepts'.\\",\\n        \\"Assumes comprehension of maximally effective, generalized, and LLM-optimized instructions.\\",\\n        \\"Assumes the ability to analyze both previous and new provided input.\\",\\n        \\"Assumes knowledge of what makes a system message instruction maximally actionable.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Enumerate sub-goals\\",\\n      \\"details\\": [\\n        \\"Parse and understand the provided conversation history (not provided in the prompt).\\",\\n        \\"Distill all relevant insights from prior conversation turns and newly provided input.\\",\\n        \\"Define what makes a sequence of system_message instructions 'maximally effective', 'LLM-optimized', and 'generalized'.\\",\\n        \\"Devise ways to incorporate both past analysis and new insights into prompt creation.\\",\\n        \\"Draft a one-line prompt that requests system_message instructions matching the criteria.\\",\\n        \\"Ensure the result is consistently actionable and value-maximizing.\\"\\n      ]\\n    },\\n    {\\n      \\"step\\": \\"Reveal potential blockers or ambiguities\\",\\n      \\"details\\": [\\n        \\"Lack of actual conversation history to analyze.\\",\\n        \\"Unclear definitions for terms like 'transformation concepts', 'actionable value', 'LLM-optimized'.\\",\\n        \\"Ambiguity in how to merge historical context with new input for instruction sequence optimization.\\",\\n        \\"The meaning of 'one-line prompt' could vary.\\",\\n        \\"Implicit requirement to be succinct yet exhaustive in the prompt.\\"\\n      ]\\n    }\\n  ]\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"input_type\\": \\"Structured Data\\",\\n  \\"structural_category\\": \\"Hierarchical List/Object with Goal Mapping\\",\\n  \\"processing_pathway\\": \\"Extract and organize analytical steps; suitable for decomposition, mapping, and workflow outlining\\"\\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n\\\`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}\\\`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"input_type\\": \\"Structured Data\\",\\n  \\"structural_category\\": \\"Hierarchical List/Object with Goal Mapping\\",\\n  \\"processing_pathway\\": \\"Extract and organize analytical steps; suitable for decomposition, mapping, and workflow outlining\\"\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"instruction_format\\": \\"Review the provided conversation history. Summarize the answer to the following task: Draft a one-line LLM-optimized prompt that builds upon existing transformation concepts to produce an optimally effective sequence of generalized and LLM-optimized 'system_message' instructions. Leverage insights from both previous history and newly provided inputs to continually maximize actionable value.\\"\\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\\n\\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n\\n\\\`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}\\\`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"instruction_format\\": \\"Review the provided conversation history. Summarize the answer to the following task: Draft a one-line LLM-optimized prompt that builds upon existing transformation concepts to produce an optimally effective sequence of generalized and LLM-optimized 'system_message' instructions. Leverage insights from both previous history and newly provided inputs to continually maximize actionable value.\\"\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"enhancement_score\\": 1.1,\\n  \\"flaw_analysis\\": \\"The so-called 'improvement' strips away the intricate scaffolding of the original and strangles nuance under the pretense of clarity. Core elements concerning 'consistently maximizing actionable value' are weakened: 'continually maximize' merely gestures, while the explicit recursive goal of 'consistently' is diluted. The original's imperative—'Employ insights from prior history and analysis'—is reduced to the limp 'leverage insights', obliterating the analytical-taking-action distinction. The enhanced version commits informational attrition by omitting the methodical dual-sourcing ('analyzing the *newly* provided input' versus a vague 'newly provided inputs'), deprioritizing process and analytical rigor. It introduces clarity ambiguities: 'optimally effective' divorces the sequence from actionable, context-linked goals; the phrase 'Draft a one-line LLM-optimized prompt' is neutral yet eliminates the explicit iterative instruction-crafting sense recurring in the original. Noise is injected by compaction: the briefed tone becomes generic, undermining specificity and shifting from technical, procedural precision to administrative blandness. Impact is diluted as the drive to 'consistently maximize actionable value' is less precise; register and tone shift to bureaucratic, rendering the instruction more an administrative note than a technical blueprint. Coherence is compromised by flattening the structure—interrelations (sequence, history, new input, system message) are conflated, so dependencies and process coherence collapse. Ambiguity score: 7/10. Flaws: reductionism, context-obscuration, process-blurring, and terminological decimation.\\",\\n  \\"alternative_enhancements\\": [\\n    \\"Synthesize a one-line LLM-optimized prompt that, building upon established transformation concepts, orchestrates a robust sequence of generalized and LLM-precise 'system_message' instructions by rigorously analyzing both prior conversational history and the latest input, with the explicit aim of sustaining maximal actionable value extraction.\\",\\n    \\"Formulate a one-line LLM-compatible prompt that integrates prior transformation frameworks and newly analyzed input to assemble a generalized, high-efficacy chain of 'system_message' instructions, ensuring the ongoing maximization of actionable utility.\\",\\n    \\"Construct a one-line LLM-optimized directive which, informed by cumulative conversation history and deep analysis of recent inputs, generates a sequentially adaptive, generalized set of 'system_message' instructions that systematically drive actionable results to their peak.\\"\\n  ]\\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\\n\\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\\n\\n\\\`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}\\\`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"enhancement_score\\": 1.1,\\n  \\"flaw_analysis\\": \\"The so-called 'improvement' strips away the intricate scaffolding of the original and strangles nuance under the pretense of clarity. Core elements concerning 'consistently maximizing actionable value' are weakened: 'continually maximize' merely gestures, while the explicit recursive goal of 'consistently' is diluted. The original's imperative—'Employ insights from prior history and analysis'—is reduced to the limp 'leverage insights', obliterating the analytical-taking-action distinction. The enhanced version commits informational attrition by omitting the methodical dual-sourcing ('analyzing the *newly* provided input' versus a vague 'newly provided inputs'), deprioritizing process and analytical rigor. It introduces clarity ambiguities: 'optimally effective' divorces the sequence from actionable, context-linked goals; the phrase 'Draft a one-line LLM-optimized prompt' is neutral yet eliminates the explicit iterative instruction-crafting sense recurring in the original. Noise is injected by compaction: the briefed tone becomes generic, undermining specificity and shifting from technical, procedural precision to administrative blandness. Impact is diluted as the drive to 'consistently maximize actionable value' is less precise; register and tone shift to bureaucratic, rendering the instruction more an administrative note than a technical blueprint. Coherence is compromised by flattening the structure—interrelations (sequence, history, new input, system message) are conflated, so dependencies and process coherence collapse. Ambiguity score: 7/10. Flaws: reductionism, context-obscuration, process-blurring, and terminological decimation.\\",\\n  \\"alternative_enhancements\\": [\\n    \\"Synthesize a one-line LLM-optimized prompt that, building upon established transformation concepts, orchestrates a robust sequence of generalized and LLM-precise 'system_message' instructions by rigorously analyzing both prior conversational history and the latest input, with the explicit aim of sustaining maximal actionable value extraction.\\",\\n    \\"Formulate a one-line LLM-compatible prompt that integrates prior transformation frameworks and newly analyzed input to assemble a generalized, high-efficacy chain of 'system_message' instructions, ensuring the ongoing maximization of actionable utility.\\",\\n    \\"Construct a one-line LLM-optimized directive which, informed by cumulative conversation history and deep analysis of recent inputs, generates a sequentially adaptive, generalized set of 'system_message' instructions that systematically drive actionable results to their peak.\\"\\n  ]\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"input_type\\": \\"structured_data_object\\",\\n  \\"structural_category\\": \\"multi-field JSON with analytical critique and suggestions\\",\\n  \\"processing_pathway\\": \\"process_as_structured_analytical_review_with_alternative_generation\\"\\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n\\\`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}\\\`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"input_type\\": \\"structured_data_object\\",\\n  \\"structural_category\\": \\"multi-field JSON with analytical critique and suggestions\\",\\n  \\"processing_pathway\\": \\"process_as_structured_analytical_review_with_alternative_generation\\"\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"instruction_format\\": \\"Review the following conversation history. Summarize the optimal method for constructing a one-line LLM-optimized prompt that extends current transformation concepts into an effective sequence of generalizable system_message instructions. Incorporate insights from previous exchanges and recent input analyses to consistently maximize actionable value in your synthesis.\\"\\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\\n\\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\n\\n\\\`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}\\\`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"instruction_format\\": \\"Review the following conversation history. Summarize the optimal method for constructing a one-line LLM-optimized prompt that extends current transformation concepts into an effective sequence of generalizable system_message instructions. Incorporate insights from previous exchanges and recent input analyses to consistently maximize actionable value in your synthesis.\\"\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"enhancement_score\\": 1.1,\\n  \\"flaw_analysis\\": \\"The enhanced instruction format discards key procedural elements from the original, notably the repeated emphasis on synthesizing both 'llm-optimized' and 'generalized' instructions. While the original prompt utilizes iterative, explicit motivation ('building upon...to yield...'), the enhancement reduces this dynamic to a static summarization process, thus flattening the nuance concerning continual leverage of both past and new data. Explicit commands to 'leverage insights derived from previous history and... analyzing newly provided input' are reduced to the limp connector 'incorporate insights from previous exchanges and recent input analyses', which obfuscates the necessity for dual-source cross-referencing and active data synthesis. Tone shifts from the original's direct imperative framing ('do so with the aim...') to a passive, depersonalized request to 'Summarize the optimal method', severely diluting intended urgency and agency. Clarity suffers due to this abstraction; the reader receives less directive pressure to actively engineer improvements or results, weakening actionable output. Ambiguity increases as the reformulation loses direct mapping between each process step and the resulting goal; the single target ('synthesize instructions to maximize value') is now engulfed in a high-level generality that obscures procedural specifics. The noise level rises due to excessive abstraction and imprecise reference to 'your synthesis', a phrase with unclear ownership and method, introducing stylistic mush that further erodes instructional force. Coherence weakens further by collapsing complex, multi-phase instruction into a single summarization directive, a move that destroys the scaffolded procedure inherent in the original and leaves execution steps underdefined. Overall: the enhancement demonstrates severe information loss, tonal regression, ambiguous phrasing, and procedural collapse.\\",\\n  \\"alternative_enhancements\\": [\\n    \\"Assimilate prior transformation concepts and recent input analyses to engineer a concise, LLM-optimized system_message sequence that generalizes and maximizes actionable output; synthesize continuously from historical and new data for optimal value.\\",\\n    \\"Formulate a one-line, LLM-tuned prompt that iteratively refines generalizable system_message instructions—drawing explicitly from both previous conversational context and newly presented material—to guarantee maximized actionable results.\\",\\n    \\"Synthesize a single, LLM-optimized system_message directive by methodically integrating both prior history and fresh input analyses, targeting consistent maximization of actionable value through generalized instruction design.\\"\\n  ]\\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\\n\\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\n\\n\\\`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}\\\`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\\n  \\"initial_prompt\\": \\"\\\\n'\\\`\\\`\\\`[Initial Prompt]: \\\\\\"please familiarize yourself with the following conversation history, and summarize the answer to the question: \\\\\\\"how would you write a one-line llm-optimized prompt for 'Building upon the existing transformation concepts to yield a maximally effective sequence of **llm-optimized** and **generalized** \\\`system_message\\\` instructions, leverage insights derived from previous history and from analyzing the *newly* provided input, do so with the aim of *consistently* maximizing actionable value. Employ insights from prior history and analysis of newly provided sequences to consistently maximize actionable value.'\\\\\\\"\\\\\\\"\\\`\\\`\\\`'\\\\n\\\\n\\",\\n  \\"enhancement_score\\": 1.1,\\n  \\"flaw_analysis\\": \\"The enhanced instruction format discards key procedural elements from the original, notably the repeated emphasis on synthesizing both 'llm-optimized' and 'generalized' instructions. While the original prompt utilizes iterative, explicit motivation ('building upon...to yield...'), the enhancement reduces this dynamic to a static summarization process, thus flattening the nuance concerning continual leverage of both past and new data. Explicit commands to 'leverage insights derived from previous history and... analyzing newly provided input' are reduced to the limp connector 'incorporate insights from previous exchanges and recent input analyses', which obfuscates the necessity for dual-source cross-referencing and active data synthesis. Tone shifts from the original's direct imperative framing ('do so with the aim...') to a passive, depersonalized request to 'Summarize the optimal method', severely diluting intended urgency and agency. Clarity suffers due to this abstraction; the reader receives less directive pressure to actively engineer improvements or results, weakening actionable output. Ambiguity increases as the reformulation loses direct mapping between each process step and the resulting goal; the single target ('synthesize instructions to maximize value') is now engulfed in a high-level generality that obscures procedural specifics. The noise level rises due to excessive abstraction and imprecise reference to 'your synthesis', a phrase with unclear ownership and method, introducing stylistic mush that further erodes instructional force. Coherence weakens further by collapsing complex, multi-phase instruction into a single summarization directive, a move that destroys the scaffolded procedure inherent in the original and leaves execution steps underdefined. Overall: the enhancement demonstrates severe information loss, tonal regression, ambiguous phrasing, and procedural collapse.\\",\\n  \\"alternative_enhancements\\": [\\n    \\"Assimilate prior transformation concepts and recent input analyses to engineer a concise, LLM-optimized system_message sequence that generalizes and maximizes actionable output; synthesize continuously from historical and new data for optimal value.\\",\\n    \\"Formulate a one-line, LLM-tuned prompt that iteratively refines generalizable system_message instructions—drawing explicitly from both previous conversational context and newly presented material—to guarantee maximized actionable results.\\",\\n    \\"Synthesize a single, LLM-optimized system_message directive by methodically integrating both prior history and fresh input analyses, targeting consistent maximization of actionable value through generalized instruction design.\\"\\n  ]\\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\\n  \\"instruction_format\\": \\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output.\\"\\n}",
        }
      }
    }
  ]
}
`;
