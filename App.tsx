
import React, { useState, useCallback } from 'react';
import Header from './components/Header';
import InputPanel from './components/InputPanel';
import SynthesizerPanel from './components/SynthesizerPanel';
import { generatePromptMethodology } from './services/openrouterService';
import type { PromptMethodologyResponse } from './types';

const App: React.FC = () => {
  const [result, setResult] = useState<PromptMethodologyResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);
    try {
      const response = await generatePromptMethodology();
      setResult(response);
    } catch (e) {
      if (e instanceof Error) {
        setError(e.message);
      } else {
        setError('An unknown error occurred.');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <div className="min-h-screen bg-slate-900 font-sans">
      <Header />
      <main className="container mx-auto p-4 md:p-8">
        <div className="grid grid-cols-1 gap-8">
            <InputPanel />
            <SynthesizerPanel
                onGenerate={handleGenerate}
                isLoading={isLoading}
                error={error}
                result={result}
            />
        </div>
        <footer className="text-center text-slate-500 mt-12 text-sm">
            <p>Powered by OpenRouter (Claude 3.5 Sonnet). UI crafted with React & Tailwind CSS.</p>
        </footer>
      </main>
    </div>
  );
};

export default App;
