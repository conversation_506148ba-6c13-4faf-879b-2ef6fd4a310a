# Technology Stack

## Core Framework
- **React 19.1.0** - Modern UI library with latest features
- **TypeScript 5.7.2** - Type-safe JavaScript development
- **Vite 6.2.0** - Fast build tool and dev server

## Styling & UI
- **Tailwind CSS** - Utility-first CSS framework (CDN)
- **Custom CSS** - Scrollbar styling and dark theme

## AI Integration
- **Google Gemini 2.5-flash** - LLM for prompt methodology generation
- **@google/genai 1.9.0** - Official Google Generative AI SDK
- **Structured JSON Schema** - Type-safe API responses

## Architecture
- **Component-based** - Modular React components
- **Service layer** - Separated API logic
- **Type definitions** - Comprehensive TypeScript interfaces
- **Environment configuration** - Secure API key management

## Development Tools
- **ESM modules** - Modern JavaScript module system
- **Import maps** - Browser-native dependency resolution
- **Hot reload** - Instant development feedback
