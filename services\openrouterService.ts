import { SOURCE_TEXT_1, SOURCE_TEXT_2, CONVERSATION_HISTORY } from '../constants';
import type { PromptMethodologyResponse } from '../types';

if (!process.env.API_KEY) {
  throw new Error("OPENROUTER_API_KEY environment variable not set");
}

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const API_KEY = process.env.API_KEY;

const JSON_SCHEMA_INSTRUCTION = `
You must respond with a valid JSON object that follows this exact structure:

{
  "methodology": {
    "title": "string",
    "steps": [
      {
        "step": number,
        "title": "string", 
        "description": "string"
      }
    ]
  },
  "formula": {
    "title": "string",
    "structure": "string",
    "components": [
      {
        "component": "string",
        "explanation": "string"
      }
    ]
  },
  "example": {
    "title": "string",
    "scenario": "string",
    "generatedPrompt": "string"
  }
}

IMPORTANT: Your response must be valid JSON only. Do not include any text before or after the JSON object.
`;

const buildPrompt = (): string => {
  return `**Primary Task:**
Familiarize yourself with all the provided context. Your goal is to synthesize this information to devise and summarize a structured methodology for constructing a one-line, LLM-optimized prompt. This prompt should be designed to generate a maximally effective sequence of LLM-optimized and generalized system_message instructions.

**Core Requirements:**
1. **Leverage Multi-Source Insights:** Integrate principles from the "Directional Transformation" framework (Source 1), the summary of public frameworks (Source 2), AND the iterative refinement process demonstrated in the "Conversation History" example.
2. **Maximize Actionable Value:** The methodology's explicit aim is to consistently maximize the actionable value of the generated prompts.
3. **Ensure Continual Improvement:** The methodology must include structured, procedural steps that facilitate continual improvement.

**Context and Source Material:**

**Source 1: A custom, detailed framework for "Directional Transformation"**
This document outlines a theoretical framework for prompt engineering.
${SOURCE_TEXT_1}

**Source 2: A summary of existing public frameworks (MCP, AutoGen, Activation Steering, etc.)**
This document summarizes real-world, industry-standard frameworks.
${SOURCE_TEXT_2}

**Conversation History: An Example of the Desired Process in Action**
This JSON object demonstrates an iterative process of prompt refinement (Decomposition -> Conversion -> Critique -> Improvement). Familiarize yourself with this process, as it is a key example of the methodology you need to devise.
${CONVERSATION_HISTORY}

**Final Output Instructions:**
Based on your synthesis of ALL the provided information (Source 1, Source 2, and the Conversation History), generate a JSON object that defines the structured methodology you have devised.

${JSON_SCHEMA_INSTRUCTION}`;
};

export const generatePromptMethodology = async (): Promise<PromptMethodologyResponse> => {
  try {
    const prompt = buildPrompt();
    
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'LLM Prompt Synthesizer'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error("Invalid response format from OpenRouter API");
    }

    const content = data.choices[0].message.content.trim();
    
    // Try to parse the JSON response
    let parsedJson;
    try {
      parsedJson = JSON.parse(content);
    } catch (parseError) {
      throw new Error(`Failed to parse JSON response: ${content}`);
    }
    
    // Basic validation to ensure the parsed object matches the expected structure
    if (!parsedJson.methodology || !parsedJson.formula || !parsedJson.example) {
        throw new Error("API response is missing required fields.");
    }

    return parsedJson as PromptMethodologyResponse;

  } catch (error) {
    console.error("Error generating prompt methodology:", error);
    if (error instanceof Error) {
        throw new Error(`Failed to generate content from OpenRouter API: ${error.message}`);
    }
    throw new Error("An unknown error occurred while communicating with the OpenRouter API.");
  }
};
