
import React, { useState } from 'react';
import { SOURCE_TEXT_1_ABBREVIATED, SOURCE_TEXT_2 } from '../constants';

const InputPanel: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-slate-800/50 rounded-lg border border-slate-700">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full text-left p-4 font-semibold text-slate-300 hover:bg-slate-700/50 flex justify-between items-center rounded-t-lg transition-colors"
      >
        <span>Source Material for Synthesis</span>
        <svg
          className={`w-5 h-5 transition-transform transform ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      {isExpanded && (
        <div className="p-4 border-t border-slate-700 max-h-96 overflow-y-auto">
          <div className="space-y-4">
            <div>
                <h3 className="font-bold text-indigo-400 mb-2">Source 1: Custom Framework</h3>
                <pre className="bg-slate-900 p-3 rounded-md text-xs text-slate-400 whitespace-pre-wrap font-mono">
                  {SOURCE_TEXT_1_ABBREVIATED}
                </pre>
            </div>
            <div>
                <h3 className="font-bold text-cyan-400 mb-2">Source 2: Public Frameworks Summary</h3>
                <pre className="bg-slate-900 p-3 rounded-md text-xs text-slate-400 whitespace-pre-wrap font-mono">
                  {SOURCE_TEXT_2}
                </pre>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InputPanel;
