
import React from 'react';

interface ErrorDisplayProps {
  error: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  return (
    <div className="p-6 bg-red-900/20 border border-red-500 rounded-lg text-center">
      <h3 className="text-lg font-semibold text-red-400">An Error Occurred</h3>
      <p className="mt-2 text-red-300 font-mono text-sm bg-red-900/30 p-3 rounded">{error}</p>
    </div>
  );
};

export default ErrorDisplay;
