
export interface MethodologyStep {
  step: number;
  title: string;
  description: string;
}

export interface FormulaComponent {
  component: string;
  explanation: string;
}

export interface PromptMethodologyResponse {
  methodology: {
    title: string;
    steps: MethodologyStep[];
  };
  formula: {
    title: string;
    structure: string;
    components: FormulaComponent[];
  };
  example: {
    title: string;
    scenario: string;
    generatedPrompt: string;
  };
}
