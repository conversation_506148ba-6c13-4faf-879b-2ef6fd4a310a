
import React from 'react';
import type { PromptMethodologyResponse } from '../types';
import { LightbulbIcon, BeakerIcon, SparklesIcon } from './icons';

interface ResultDisplayProps {
  data: PromptMethodologyResponse;
}

const ResultDisplay: React.FC<ResultDisplayProps> = ({ data }) => {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Methodology Section */}
      <section>
        <div className="flex items-center gap-3 mb-4">
          <LightbulbIcon className="w-8 h-8 text-cyan-400" />
          <h2 className="text-2xl font-bold text-cyan-400">{data.methodology.title}</h2>
        </div>
        <div className="space-y-4">
          {data.methodology.steps.map((step) => (
            <div key={step.step} className="p-4 bg-slate-800/50 border-l-4 border-cyan-500 rounded-r-lg">
              <h3 className="font-semibold text-slate-200">Step {step.step}: {step.title}</h3>
              <p className="mt-1 text-slate-400">{step.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Formula Section */}
      <section>
        <div className="flex items-center gap-3 mb-4">
          <BeakerIcon className="w-8 h-8 text-indigo-400" />
          <h2 className="text-2xl font-bold text-indigo-400">{data.formula.title}</h2>
        </div>
        <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-700">
          <p className="font-mono text-sm bg-slate-900 p-4 rounded-md text-indigo-300 mb-4 whitespace-pre-wrap">
            {data.formula.structure}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {data.formula.components.map((comp) => (
              <div key={comp.component}>
                <h4 className="font-semibold text-slate-200">{comp.component}</h4>
                <p className="text-sm text-slate-400">{comp.explanation}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Example Section */}
      <section>
        <div className="flex items-center gap-3 mb-4">
          <SparklesIcon className="w-8 h-8 text-amber-400" />
          <h2 className="text-2xl font-bold text-amber-400">{data.example.title}</h2>
        </div>
        <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-700">
          <div className="mb-4">
            <h4 className="font-semibold text-slate-200">Scenario:</h4>
            <p className="text-slate-400 italic">"{data.example.scenario}"</p>
          </div>
          <div>
            <h4 className="font-semibold text-slate-200">Generated One-Line Meta-Prompt:</h4>
            <p className="font-mono text-sm bg-slate-900 p-4 rounded-md text-amber-300 whitespace-pre-wrap mt-2">
              {data.example.generatedPrompt}
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ResultDisplay;
