
import React from 'react';
import type { PromptMethodologyResponse } from '../types';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import ResultDisplay from './ResultDisplay';
import { WandIcon } from './icons';

interface SynthesizerPanelProps {
  onGenerate: () => void;
  isLoading: boolean;
  error: string | null;
  result: PromptMethodologyResponse | null;
}

const SynthesizerPanel: React.FC<SynthesizerPanelProps> = ({ onGenerate, isLoading, error, result }) => {
  return (
    <div className="bg-slate-800/50 p-6 rounded-lg border border-slate-700 space-y-6 min-h-[400px] flex flex-col">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-slate-100">Prompt Methodology Synthesizer</h2>
        <p className="text-slate-400 mt-1">
          Click the button to synthesize the source material into an actionable, one-line prompt formula.
        </p>
      </div>
      
      {!result && !isLoading && !error && (
        <div className="flex-grow flex items-center justify-center">
             <button
                onClick={onGenerate}
                disabled={isLoading}
                className="inline-flex items-center gap-2 px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg shadow-lg hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-indigo-500 disabled:bg-slate-500 disabled:cursor-not-allowed transition-all transform hover:scale-105"
             >
                <WandIcon className="w-5 h-5" />
                <span>Synthesize with OpenRouter</span>
            </button>
        </div>
      )}

      {isLoading && <LoadingSpinner />}
      {error && !isLoading && <ErrorDisplay error={error} />}
      {result && !isLoading && !error && <ResultDisplay data={result} />}
    </div>
  );
};

export default SynthesizerPanel;
