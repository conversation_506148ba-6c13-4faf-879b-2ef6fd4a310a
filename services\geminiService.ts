
import { GoogleGenAI, Type } from "@google/genai";
import { SOURCE_TEXT_1, SOURCE_TEXT_2, CONVERSATION_HISTORY } from '../constants';
import type { PromptMethodologyResponse } from '../types';

if (!process.env.API_KEY) {
  throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const responseSchema = {
  type: Type.OBJECT,
  properties: {
    methodology: {
      type: Type.OBJECT,
      properties: {
        title: { type: Type.STRING },
        steps: {
          type: Type.ARRAY,
          items: {
            type: Type.OBJECT,
            properties: {
              step: { type: Type.INTEGER },
              title: { type: Type.STRING },
              description: { type: Type.STRING }
            }
          }
        }
      }
    },
    formula: {
      type: Type.OBJECT,
      properties: {
        title: { type: Type.STRING },
        structure: { type: Type.STRING },
        components: {
          type: Type.ARRAY,
          items: {
            type: Type.OBJECT,
            properties: {
              component: { type: Type.STRING },
              explanation: { type: Type.STRING }
            }
          }
        }
      }
    },
    example: {
      type: Type.OBJECT,
      properties: {
        title: { type: Type.STRING },
        scenario: { type: Type.STRING },
        generatedPrompt: { type: Type.STRING }
      }
    }
  }
};

const buildPrompt = (): string => {
  return `
    **Primary Task:**
    Familiarize yourself with all the provided context. Your goal is to synthesize this information to devise and summarize a structured methodology for constructing a one-line, LLM-optimized prompt. This prompt should be designed to generate a maximally effective sequence of LLM-optimized and generalized system_message instructions.

    **Core Requirements:**
    1.  **Leverage Multi-Source Insights:** Integrate principles from the "Directional Transformation" framework (Source 1), the summary of public frameworks (Source 2), AND the iterative refinement process demonstrated in the "Conversation History" example.
    2.  **Maximize Actionable Value:** The methodology's explicit aim is to consistently maximize the actionable value of the generated prompts.
    3.  **Ensure Continual Improvement:** The methodology must include structured, procedural steps that facilitate continual improvement.

    **Context and Source Material:**

    **Source 1: A custom, detailed framework for "Directional Transformation"**
    This document outlines a theoretical framework for prompt engineering.
    ${SOURCE_TEXT_1}

    **Source 2: A summary of existing public frameworks (MCP, AutoGen, Activation Steering, etc.)**
    This document summarizes real-world, industry-standard frameworks.
    ${SOURCE_TEXT_2}

    **Conversation History: An Example of the Desired Process in Action**
    This JSON object demonstrates an iterative process of prompt refinement (Decomposition -> Conversion -> Critique -> Improvement). Familiarize yourself with this process, as it is a key example of the methodology you need to devise.
    ${CONVERSATION_HISTORY}

    **Final Output Instructions:**
    Based on your synthesis of ALL the provided information (Source 1, Source 2, and the Conversation History), generate a JSON object that defines the structured methodology you have devised. Your output must strictly adhere to the defined JSON schema.
  `;
};

export const generatePromptMethodology = async (): Promise<PromptMethodologyResponse> => {
  try {
    const prompt = buildPrompt();
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: responseSchema,
        temperature: 0.2,
      },
    });

    const jsonText = response.text.trim();
    const parsedJson = JSON.parse(jsonText);
    
    // Basic validation to ensure the parsed object matches the expected structure
    if (!parsedJson.methodology || !parsedJson.formula || !parsedJson.example) {
        throw new Error("API response is missing required fields.");
    }

    return parsedJson as PromptMethodologyResponse;

  } catch (error) {
    console.error("Error generating prompt methodology:", error);
    if (error instanceof Error) {
        throw new Error(`Failed to generate content from Gemini API: ${error.message}`);
    }
    throw new Error("An unknown error occurred while communicating with the Gemini API.");
  }
};
